#include <bits/stdc++.h>
#define endl '\n'
int const maxn = 3005;
int a[maxn+5];
int dp[maxn+5][maxn+5];
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int n;
    cin >> n;
    for(int i = 1; i <= n; i++)
    {
        cin >> a[i];
    }
    int ans = 1;
    for(int i = 1; i <= n; i++)
    {
        for(int j = 1; j <= i-1; j++)
        {
            int d = a[i] - a[j];
            if(d < 0) continue;
            if(dp[j][d] > 0) 
            dp[i][d] = max(dp[i][d], dp[j][d] + 1);
            else 
              dp[i][d] = 2;

        ans = max(ans, dp[i][d]);
        }
    }
    cout << ans;
    return 0;
}