#include <bits/stdc++.h>
using namespace std;
#define endl '\n'
int n;
int a[20];

bool dfs(int i, int sum) 
{
    if (i == n)
    {
        return (sum % 360 == 0);
    }

    if (dfs(i+1, (sum + a[i]) % 360)) return true;

    if (dfs(i+1, (sum - a[i] + 360) % 360)) return true;
    return false;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    cin >> n;
    for (int i = 0; i < n; i++) cin >> a[i];

    if (dfs(0, 0)) cout << "YES" << endl;
    else cout << "NO" << endl;

    return 0;
}
