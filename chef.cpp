#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

int main() 
{
    ios::sync_with_stdio(0);
    cin.tie(0);

    int T;
    cin >> T;

    while (T--) 
    {
        int N;
        unsigned int Y;
        cin >> N >> Y;

        unsigned int current_or = 0;
        for (int i = 0; i < N; i++) 
        {
            unsigned int a;
            cin >> a;
            current_or |= a;
        }


        if ((current_or | Y) != Y)
       {
            cout << -1 << endl;
        } 
        else 
        {
 
            unsigned int X = Y ^ current_or;
            cout << X << endl;
        }
    }

    return 0;
}