#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int t;
    cin >> t;
    while(t--)
    {
        int l,r;
        cin >> l >> r;
        if(l % 2 == 0 && l + 3 <= r)
        {
            cout << l << " " << l + 1 << " " << l + 2 <<  " " << l + 3<< endl;
        }
        else if(l % 2 == 1 && l + 4 <= r )
        {
            cout << l + 1 << " " << l + 2 << " " << l + 3 << " " << l + 4 << endl;
        }
        else
        {
            cout << -1;
        }
        cout << endl;
    }
    return 0;
}