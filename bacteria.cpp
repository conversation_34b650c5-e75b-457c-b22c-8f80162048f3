#include <bits/stdc++.h>
#define endl '\n'
using namespace std;

void init()
{
    ios_base::sync_with_stdio(0);
    cin.tie(0);
    cout.tie(0);
}

int main()
{
    init();
    int x;
    cin >> x;


    // Cách 1: Dùng bitset (32 bit)
    bitset<32> bits(x);
    string binary = bits.to_string();

    // Loại bỏ leading zeros
    size_t firstOne = binary.find('1');
    if (firstOne != string::npos) {
        binary = binary.substr(firstOne);
    } else {
        binary = "0";
    }

    // Đếm số bit 1
    int count = bits.count();

    cout << "Binary: " << binary << endl;
    cout << "Answer: " << count;
    return 0;
}