#include <bits/stdc++.h>
#include <array>
#define endl '\n'
using namespace std;

bool isPrime(long long n) {
    if (n < 2) return false;
    if (n % 2 == 0) return (n == 2);
    for (long long i = 3; i * i <= n; i += 2) {
        if (n % i == 0) return false;
    }
    return true;
}

int main() {
    ios::sync_with_stdio(0);
    cin.tie(0);

    int T;
    cin >> T;
    while (T--) {
        long long X, Y;
        cin >> X >> Y;

        long long Z = X ^ Y;


        long long A = (Y + Z - X) / 2;
        long long B = (X + Z - Y) / 2;
        long long C = (X + Y - Z) / 2;

        if (A > 1 && B > 1 && C > 1 &&
            A != B && B != C && A != C &&
            isPrime(A) && isPrime(B) && isPrime(C) &&
            (A ^ B) == X && (B ^ C) == Y && (C ^ A) == Z) {

            vector<long long> ans = {A, B, C};
            sort(ans.begin(), ans.end());
            cout << ans[0] << " " << ans[1] << " " << ans[2] << endl;
        } else {
            cout << "-1" << endl;
        }
    }
    return 0;
}
